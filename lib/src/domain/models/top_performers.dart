class TopPerformers {
  String id;
  String name;
  int downlineAgentsCount;
  int monthlyRevenue;
  int monthlySalesCount;
  String associatedBroker;
  int rank;

  TopPerformers({
    required this.id,
    required this.name,
    required this.downlineAgentsCount,
    required this.monthlyRevenue,
    required this.monthlySalesCount,
    required this.associatedBroker,
    required this.rank,
  });

  factory TopPerformers.fromJson(Map<String, dynamic> json) {
    return TopPerformers(
      id: json['id'] as String,
      name: json['name'] as String,
      downlineAgentsCount: json['downlineAgentsCount'] as int,
      monthlyRevenue: json['monthlyRevenue'] as int,
      monthlySalesCount: json['monthlySalesCount'] as int,
      associatedBroker: json['associatedBroker'] as String,
      rank: json['rank'] as int,
    );
  }

  static List<TopPerformers> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => TopPerformers.fromJson(json as Map<String, dynamic>)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'downlineAgentsCount': downlineAgentsCount,
      'monthlyRevenue': monthlyRevenue,
      'monthlySalesCount': monthlySalesCount,
      'associatedBroker': associatedBroker,
      'rank': rank,
    };
  }

  TopPerformers copyWith({
    String? id,
    String? name,
    int? downlineAgentsCount,
    int? monthlyRevenue,
    int? monthlySalesCount,
    String? associatedBroker,
    int? rank,
  }) => TopPerformers(
    id: id ?? this.id,
    name: name ?? this.name,
    downlineAgentsCount: downlineAgentsCount ?? this.downlineAgentsCount,
    monthlyRevenue: monthlyRevenue ?? this.monthlyRevenue,
    monthlySalesCount: monthlySalesCount ?? this.monthlySalesCount,
    associatedBroker: associatedBroker ?? this.associatedBroker,
    rank: rank ?? this.rank,
  );
}
