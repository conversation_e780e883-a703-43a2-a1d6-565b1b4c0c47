import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../cubit/top_performers/top_performers_cubit.dart';
import '/src/core/enum/user_role.dart';

import '../../../../core/config/app_strings.dart';
import '../../../../domain/models/agent.dart';
import '../../../../domain/models/broker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/config/json_consts.dart';
import '../../../../domain/models/user.dart';
import '../../../cubit/user/user_cubit.dart';
import 'sales_by_brokers_card.dart';

class CommissionCard extends HookWidget {
  const CommissionCard({super.key});

  @override
  Widget build(BuildContext context) {
    final showBrokers = useState<bool>(true);
    final topTenCommissionedBrokers = useState<List<Broker>>([]);
    final topTenCommissionedAgents = useState<List<Agent>>([]);
    final topTenSalesBrokers = useState<List<Broker>>([]);
    final topTenSalesAgents = useState<List<Agent>>([]);
    final user = context.watch<UserCubit>().state.user;
    final userRole = user?.role ?? "";

    // Initialize data
    useEffect(() {
      Future.microtask(() async {
        await context.read<TopPerformersCubit>().getTopPerformers();
      });

      final brokers = [...brokersListJson];
      brokers.sort((a, b) => b.totalCommission.compareTo(a.totalCommission));
      topTenCommissionedBrokers.value = brokers.take(10).toList();
      brokers.sort((a, b) => b.sales.compareTo(a.sales));
      topTenSalesBrokers.value = brokers.take(10).toList();

      final agents = [...agentListJson];
      agents.sort((a, b) => b.amount.compareTo(a.amount));
      topTenCommissionedAgents.value = agents.take(10).toList();
      agents.sort((a, b) => b.sales.compareTo(a.sales));
      topTenSalesAgents.value = agents.take(10).toList();

      return null;
    }, []);

    final Size size = MediaQuery.of(context).size;
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commissionCardBreakPoint;

    return LayoutBuilder(
      builder: (context, constraints) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Container(
            constraints: BoxConstraints(maxHeight: size.height),
            width: Responsive.isDesktop(context)
                ? size.width * 0.2
                : size.width,
            decoration: _buildBoxDecoration(),
            child: Responsive.isTablet(context)
                ? Container(
                    height: size.height / 1.6,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildHeader(isSmallView, size),
                              _buildTabRow(size, showBrokers, user),

                              const SizedBox(height: defaultPadding),
                              _buildPerformersList(
                                isSmallView,
                                user?.role == UserRole.brokerage ||
                                        user?.role == UserRole.officeStaff
                                    ? true
                                    : user?.role == UserRole.agent
                                    ? false
                                    : showBrokers.value,
                                topTenCommissionedBrokers,
                                topTenCommissionedAgents,
                                user,
                              ),
                            ],
                          ),
                        ),

                        Expanded(
                          child: ValueListenableBuilder(
                            valueListenable: showBrokers,
                            builder: (context, value, child) {
                              return SalesByBrokersCard(
                                brokersList: topTenCommissionedBrokers.value,
                                agentsList: topTenCommissionedAgents.value,
                                isBrokerView: showBrokers.value,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(isSmallView, size),
                      _buildTabRow(size, showBrokers, user),
                      const SizedBox(height: defaultPadding),
                      _buildPerformersList(
                        isSmallView,
                        user?.role == UserRole.brokerage ||
                                user?.role == UserRole.officeStaff ||
                                user?.role == UserRole.agent
                            ? false
                            : showBrokers.value,
                        topTenCommissionedBrokers,
                        topTenCommissionedAgents,
                        user,
                      ),
                      const SizedBox(height: defaultPadding),

                      Flexible(
                        child: SalesByBrokersCard(
                          isBrokerView:
                              user?.role == UserRole.brokerage ||
                                  user?.role == UserRole.officeStaff ||
                                  user?.role == UserRole.agent
                              ? false
                              : showBrokers.value,
                          brokersList: topTenSalesBrokers.value,
                          agentsList: topTenSalesAgents.value,
                        ),
                      ),
                      const SizedBox(height: defaultPadding / 1.5),
                    ],
                  ),
          ),
        );
      },
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.commissionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildHeader(bool isSmallView, Size size) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: defaultPadding,
        horizontal: defaultPadding,
      ),
      color: AppTheme.commissionCardDarkColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            topPerformers,
            style: AppFonts.semiBoldTextStyle(
              size.width < 1330 && size.width > 1200 ? 16 : 18,
              color: Colors.white,
            ),
          ),
          _buildMonthSelection(size),
        ],
      ),
    );
  }

  Widget _buildMonthSelection(Size size) {
    bool restrictSize = size.width >= 1200 && size.width < 1330;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: restrictSize ? 5 : 12,
        vertical: 5,
      ),
      decoration: BoxDecoration(
        color: AppTheme.commissionDropDownBgColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            size.width >= 1100 && size.width < 1570
                ? monthLabel
                : selectMonthlabel,
            style: AppFonts.mediumTextStyle(
              size.width >= 1100 && size.width < 1570 ? 11 : 14,
              color: Colors.white,
            ),
          ),
          SizedBox(width: size.width < 1570 && size.width >= 1100 ? 2 : 4),
          Icon(
            Icons.keyboard_arrow_down,
            color: Colors.white,
            size: restrictSize ? 10 : 16,
          ),
        ],
      ),
    );
  }

  Widget _buildTabRow(Size size, ValueNotifier<bool> showBrokers, User? user) {
    return user?.role == UserRole.admin || user?.role == UserRole.platformOwner
        ? Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(
              defaultPadding,
              0,
              defaultPadding,
              defaultPadding,
            ),
            decoration: BoxDecoration(color: AppTheme.commissionCardDarkColor),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.commissionCardColor,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTab(brokerLabel, showBrokers.value, () {
                        showBrokers.value = true;
                      }),
                      _buildTab(agentLabel, !showBrokers.value, () {
                        showBrokers.value = false;
                      }),
                    ],
                  ),
                ),
              ),
            ),
          )
        : SizedBox.shrink();
  }

  Widget _buildTab(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.commissionDropDownBgColor
              : AppTheme.commissionCardColor,
          borderRadius: isSelected
              ? BorderRadius.circular(20)
              : BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
        ),
        child: Text(
          text,
          style: AppFonts.mediumTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildPerformersList(
    bool isSmallView,
    bool showBrokers,
    ValueNotifier<List<Broker>> topTenBrokers,
    ValueNotifier<List<Agent>> topTenAgents,
    User? user,
  ) {
    return Expanded(
      child: Container(
        color: AppTheme.commissionCardColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              commissionTab,
              textAlign: TextAlign.center,
              style: AppFonts.semiBoldTextStyle(16, color: Colors.white),
            ),
            const SizedBox(height: defaultPadding),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
                itemCount: showBrokers
                    ? topTenBrokers.value.length
                    : topTenAgents.value.length,
                itemBuilder: (context, index) {
                  final bgColor = index % 2 == 0
                      ? AppTheme.commissionCardColor
                      : AppTheme.commissionCardDarkColor;
                  if (showBrokers) {
                    final broker = topTenBrokers.value[index];
                    return _buildBrokerPerformerItem(
                      broker,
                      isSmallView,
                      bgColor,
                    );
                  } else {
                    final agent = topTenAgents.value[index];
                    return _buildAgentPerformerItem(
                      agent,
                      isSmallView,
                      bgColor,
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBrokerPerformerItem(
    Broker broker,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(broker.name, broker.agents.length, false, ''),
            _earningsWidget(broker.totalCommission),
          ],
        ),
      ),
    );
  }

  Widget _userAvatar() {
    return CircleAvatar(
      radius: 18,
      backgroundColor: Colors.white.withValues(alpha: 0.2),
      child: Icon(Icons.person, color: Colors.white, size: 18),
    );
  }

  Widget _buildAgentPerformerItem(
    Agent agent,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(
              agent.name,
              agent.agents.length,
              true,
              agent.relatedBroker,
            ),
            SizedBox(width: 12),
            _earningsWidget(agent.amount),
          ],
        ),
      ),
    );
  }

  Widget _nameSalesCountWidget(
    String name,
    int agentsCount,
    bool isAgent,
    String relatedBroker,
  ) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.semiBoldTextStyle(13, color: Colors.white),
          ),
          SizedBox(height: 3),
          Text(
            '$agentsCount $agentLabel',
            style: AppFonts.regularTextStyle(
              11,
              color: AppTheme.commissionSalesTextColor,
            ),
          ),

          if (isAgent) ...[
            Divider(color: AppTheme.commissionSalesTextColor),
            Text(
              relatedBrokerLabel,
              style: AppFonts.regularTextStyle(
                11,
                color: AppTheme.commissionSalesTextColor,
              ),
            ),
            SizedBox(height: 3),
            Text(
              relatedBroker,
              style: AppFonts.semiBoldTextStyle(13, color: AppTheme.white),
            ),
          ],
        ],
      ),
    );
  }

  Widget _earningsWidget(double totalCommission) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '\$${totalCommission.toInt()}',
          style: AppFonts.semiBoldTextStyle(14, color: Colors.white),
        ),
        SizedBox(height: 3),
        Text(
          revenueEarnedLabel,
          maxLines: 2,
          style: AppFonts.mediumTextStyle(
            10,
            color: AppTheme.commissionSalesTextColor,
          ),
        ),
      ],
    );
  }
}
