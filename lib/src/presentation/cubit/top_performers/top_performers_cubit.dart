import 'package:hydrated_bloc/hydrated_bloc.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/top_performers.dart';
import '../../../domain/repository/top_performers_repository.dart';
import '../../../core/enum/user_role.dart';

part 'top_performers_state.dart';

class TopPerformersCubit extends HydratedCubit<TopPerformersState> {
  TopPerformersCubit(this._topPerformersRepository)
    : super(TopPerformersInitial());
  final TopPerformersRepository _topPerformersRepository;

  Future<void> getTopPerformersByRole(UserRole userRole) async {
    emit(TopPerformersLoading());
    try {
      List<TopPerformers> agentTopPerformers = [];
      List<TopPerformers> brokerageTopPerformers = [];

      // Fetch data based on user role
      if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
        // For agent or broker roles, fetch only agent top performers
        agentTopPerformers = await _topPerformersRepository.getAgentTopPerformers();
      } else if (userRole == UserRole.admin || userRole == UserRole.platformOwner) {
        // For admin or platform owner, fetch both agent and brokerage top performers
        final futures = await Future.wait([
          _topPerformersRepository.getAgentTopPerformers(),
          _topPerformersRepository.getBrokerageTopPerformers(),
        ]);
        agentTopPerformers = futures[0];
        brokerageTopPerformers = futures[1];
      }

      emit(TopPerformersLoaded(
        agentTopPerformers: agentTopPerformers,
        brokerageTopPerformers: brokerageTopPerformers,
      ));
    } on ApiException catch (e) {
      emit(TopPerformersError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        TopPerformersError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  @override
  TopPerformersState? fromJson(Map<String, dynamic> json) {
    try {
      final agentTopPerformers = json['agentTopPerformers'] != null
          ? TopPerformers.fromJsonList(json['agentTopPerformers'])
          : <TopPerformers>[];
      final brokerageTopPerformers = json['brokerageTopPerformers'] != null
          ? TopPerformers.fromJsonList(json['brokerageTopPerformers'])
          : <TopPerformers>[];

      return TopPerformersLoaded(
        agentTopPerformers: agentTopPerformers,
        brokerageTopPerformers: brokerageTopPerformers,
      );
    } catch (e) {
      return TopPerformersInitial();
    }
  }

  @override
  Map<String, dynamic>? toJson(TopPerformersState state) {
    if (state is TopPerformersLoaded) {
      return {
        'agentTopPerformers': state.agentTopPerformers?.map((e) => e.toJson()).toList(),
        'brokerageTopPerformers': state.brokerageTopPerformers?.map((e) => e.toJson()).toList(),
      };
    }
    return null;
  }
}
