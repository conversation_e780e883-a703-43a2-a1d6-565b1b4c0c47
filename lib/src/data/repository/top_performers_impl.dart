import 'package:dio/dio.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/top_performers.dart';
import '../../domain/repository/top_performers_repository.dart';

class TopPerformersRepositoryImpl extends TopPerformersRepository {
  TopPerformersRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String brokerageTopPerformersUrl =
      APIConfig.brokerageTopPerformers;
  static const String agentTopPerformersUrl = APIConfig.agentTopPerformers;

  @override
  Future<List<TopPerformers>> getBrokerageTopPerformers() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(brokerageTopPerformersUrl);

      if (response.statusCode == 200) {
        return TopPerformers.fromJsonList(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchBrokerageTopPerformers,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  Future<List<TopPerformers>> getAgentTopPerformers() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(agentTopPerformersUrl);

      if (response.statusCode == 200) {
        return TopPerformers.fromJsonList(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchAgentTopPerformers,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<List<TopPerformers>> getTopPerformers() {
    // TODO: implement getTopPerformers
    throw UnimplementedError();
  }
}
